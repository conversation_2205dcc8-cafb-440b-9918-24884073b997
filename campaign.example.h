/**
 * @file campaign.example.h
 * Example campaign-specific configuration for LuminariMUD
 * 
 * This file contains example campaign-specific settings and customizations.
 * It is excluded from version control as it contains local configuration.
 * 
 * Add your campaign-specific #defines, constants, and configurations here. 
 * Copy this file to campaign.h and remove the .example extension.
 */

 #ifndef _CAMPAIGN_H_
 #define _CAMPAIGN_H_
 
 /* Add campaign-specific configuration here, if there are no defines here
    then the campaign will default to LuminariMUD */

    /* DragonLance Campaign */
// #define CAMPAIGN_DL // Uncomment this line to enable the DL campaign

    /* Forgotten Realms Campaign */
// #define CAMPAIGN_FR // Uncomment this line to enable the FR campaign

 #endif /* _CAMPAIGN_H_ */

# CLAUDE.md - AI Assistant Development Guide

This file provides comprehensive guidance for AI assistants (<PERSON>, <PERSON><PERSON>, etc.) when working with the LuminariMUD codebase. It contains essential context, patterns, and best practices for effective code assistance.

## Project Overview

LuminariMUD is a sophisticated text-based multiplayer online role-playing game (MUD) server implementing authentic Pathfinder/D&D 3.5 mechanics. Built on the proven tbaMUD/CircleMUD foundation, it features:

- **Authentic D&D 3.5 Rules**: Complete implementation of classes, races, feats, skills, and spells
- **Advanced Combat System**: Initiative-based combat with tactical positioning
- **Dynamic Scripting**: DG Scripts for interactive content and complex behaviors
- **Online Building Tools**: Comprehensive OLC system for world creation
- **MySQL Integration**: Persistent player data and world state
- **Performance Optimized**: Handles hundreds of concurrent players efficiently

### Current Version
- **LuminariMUD 2.4839** (based on tbaMUD 3.64)
- **Language**: C (C99 standard)
- **Platform**: Linux/Unix systems
- **Database**: MySQL 5.0+ or MariaDB

## Build Commands
```bash
# Build the main game executable
make

# Build everything including utilities
make all

# Clean build artifacts
make clean

# Build and run unit tests
make cutest

# Generate dependencies
make depend

# Build just utilities
make utils
```

## Running and Testing
```bash
# The main binary is output to ../bin/circle
# Run the game server (from the bin directory)
./circle

# Run unit tests
../bin/cutest
```

## Deployment
The project includes deployment scripts for different environments:
```bash
# Deploy to dev environment
./copylumbinarytodev.sh

# Deploy to live environment
./copylumbinarytolive.sh

# Similar scripts exist for Dragonlance and Faerun variants
```

## Architecture Overview
The codebase follows a modular MUD architecture:

- **comm.c** - Main entry point, game loop, and network handling
- **interpreter.c** - Command parsing and execution
- **act.*.c files** - Player commands and actions (movement, combat, etc.)
- **db.c** - World database loading and management
- **handler.c** - Core object manipulation functions
- **fight.c / magic.c / spells.c** - Combat and magic systems
- **class.c / race.c / feats.c** - Character development systems
- **dg_*.c files** - DG Scripts scripting system
- ***edit.c files** - OLC (Online Creation) tools for builders
- **gen*.c files** - Generic object/mob/room creation functions

## Important Development Notes

### Configuration Files
- **campaign.h** - Copy from `campaign.example.h` to create your local campaign configuration. This file contains campaign-specific customizations (CAMPAIGN_DL for Dragonlance, CAMPAIGN_FR for Forgotten Realms, or leave empty for default LuminariMUD).
- **mud_options.h** - Copy from `mud_options.example.h` to configure MUD-specific options.

### Code Style
- Follow existing indentation and brace style in each file
- Use the existing utility functions and macros defined in utils.h
- When adding new commands, register them in interpreter.c
- Use the existing logging macros (log(), mudlog()) for debugging

### Code Formatting
The project includes a `.clang-format` configuration file for consistent code formatting:
```bash
# Format a single file
clang-format -i filename.c

# Format all C files in current directory
clang-format -i *.c

# Format all C and header files recursively
find . -name "*.c" -o -name "*.h" | xargs clang-format -i

# Check formatting without modifying files
clang-format -n filename.c

# Format only modified lines (requires git)
git diff -U0 --no-color | clang-format-diff -i
```

### Key Systems to Understand
1. **DG Scripts** - The scripting system used for dynamic content
2. **OLC (Online Creation)** - The in-game building tools
3. **Event System** - mud_event.c handles timed events
4. **Action Queues** - actionqueues.c manages delayed actions
5. **Protocol Handling** - protocol.c manages telnet and MUD-specific protocols

### Database Integration
- MySQL support is integrated for persistent storage
- Connection handled through mysql.c
- Used for player data, mail, and other persistent information

### Recent Development Focus (2025)
- **Documentation Overhaul**: Comprehensive technical documentation system
- **Performance Optimization**: Zone resets, mobile activity, NPC casting efficiency
- **Database Schema Fixes**: Resolved missing column errors and compatibility issues
- **Memory Management**: Fixed leaks and improved allocation patterns
- **Build System**: Modern compiler compatibility and automated testing
- **Security Enhancements**: Input validation and privilege escalation fixes

## AI Assistant Guidelines

### Understanding the Codebase
1. **Read Documentation First**: Always consult the [Technical Documentation Master Index](documentation/TECHNICAL_DOCUMENTATION_MASTER_INDEX.md)
2. **Follow Existing Patterns**: Study similar functions before implementing new ones
3. **Respect Architecture**: Maintain the modular design and separation of concerns
4. **Consider Performance**: This is a real-time multiplayer system with strict performance requirements

### Code Analysis Approach
```c
// When analyzing code, consider these aspects:
// 1. Function purpose and parameters
// 2. Memory management (allocation/deallocation)
// 3. Error handling and validation
// 4. Performance implications
// 5. Thread safety (single-threaded but event-driven)
// 6. Integration with existing systems
```

### Common Patterns to Recognize

#### Character Data Access
```c
// Always validate character pointers
if (!ch) {
  log("SYSERR: %s called with NULL character", __func__);
  return;
}

// Use macros for character properties
GET_NAME(ch)     // Character name
GET_LEVEL(ch)    // Character level
GET_CLASS(ch)    // Character class
IS_NPC(ch)       // Check if NPC
```

#### Memory Management
```c
// Use CREATE macro for allocation
CREATE(new_obj, struct obj_data, 1);

// Always free allocated memory
if (obj->name) {
  free(obj->name);
  obj->name = NULL;
}

// Use appropriate cleanup functions
free_char(ch);    // For characters
extract_obj(obj); // For objects
```

#### Error Handling
```c
// Use consistent logging
log("INFO: Normal operation message");
log("SYSERR: System error occurred");
mudlog(BRF, LVL_IMMORT, TRUE, "Admin message: %s", message);

// Validate input parameters
if (!argument || !*argument) {
  send_to_char(ch, "Usage: command <required_argument>\r\n");
  return;
}
```

### Development Workflow for AI Assistants

#### 1. Information Gathering
- **Examine Related Code**: Look at similar functions and systems
- **Check Documentation**: Consult relevant technical documentation
- **Understand Context**: Consider how changes affect other systems
- **Review Recent Changes**: Check CHANGELOG.md for recent modifications

#### 2. Code Implementation
- **Follow Coding Standards**: Use existing style and conventions
- **Add Proper Documentation**: Include function headers and comments
- **Implement Error Handling**: Validate inputs and handle edge cases
- **Consider Performance**: Optimize for the real-time environment

#### 3. Testing Considerations
- **Unit Tests**: Add tests for new functionality when possible
- **Integration Testing**: Consider how changes affect existing systems
- **Memory Testing**: Check for leaks with valgrind
- **Performance Testing**: Ensure changes don't degrade performance

### Key Files and Their Purposes

#### Core Engine Files
- **`comm.c`** - Main game loop, networking, player connections
- **`interpreter.c`** - Command parsing and execution
- **`db.c`** - World data loading, database operations
- **`handler.c`** - Core object/character manipulation
- **`utils.c`** - Utility functions used throughout codebase

#### Game Systems
- **`fight.c`** - Combat mechanics and damage calculation
- **`magic.c`** - Spell casting and magical effects
- **`spells.c`** - Individual spell implementations
- **`class.c`** - Character class definitions and abilities
- **`race.c`** - Character race definitions and bonuses
- **`feats.c`** - Feat system implementation

#### Player Actions
- **`act.comm.c`** - Communication commands (say, tell, channels)
- **`act.informative.c`** - Information commands (look, examine, who)
- **`act.movement.c`** - Movement commands and mechanics
- **`act.offensive.c`** - Combat commands and actions
- **`act.other.c`** - Miscellaneous player commands
- **`act.wizard.c`** - Administrative and immortal commands

#### Building and Scripting
- **`dg_scripts.c`** - DG scripting engine
- **`dg_triggers.c`** - Script trigger system
- **`*edit.c`** - OLC (Online Level Creation) editors
- **`genolc.c`** - Generic OLC functions

#### Specialized Systems
- **`mysql.c`** - Database integration
- **`mud_event.c`** - Event system for timed actions
- **`actionqueues.c`** - Action queue management
- **`protocol.c`** - Telnet and MUD protocol handling

### Common Debugging Scenarios

#### Memory Issues
```c
// Check for NULL pointers before use
if (!ptr) {
  log("SYSERR: Unexpected NULL pointer in %s", __func__);
  return ERROR_CODE;
}

// Use valgrind for memory leak detection
// valgrind --leak-check=full ../bin/circle
```

#### Performance Problems
```c
// Profile with gprof
// make PROFILE=-pg
// gprof ../bin/circle gmon.out > profile.txt

// Log performance-critical sections
struct timeval start, end;
gettimeofday(&start, NULL);
// ... code to profile ...
gettimeofday(&end, NULL);
long diff = (end.tv_sec - start.tv_sec) * 1000000 + (end.tv_usec - start.tv_usec);
if (diff > 1000) { // Log if > 1ms
  log("PERFORMANCE: %s took %ld microseconds", __func__, diff);
}
```

#### Database Issues
```c
// Always check MySQL connection
mysql_ping(conn);

// Use proper error handling
if (mysql_query(conn, query)) {
  log("SYSERR: MySQL query failed: %s", mysql_error(conn));
  return FALSE;
}

// Free result sets
mysql_free_result(result);
```

### Integration Points to Consider

#### When Adding New Commands
1. **Register in `interpreter.c`**: Add to cmd_info table
2. **Implement Function**: Follow ACMD() pattern
3. **Add Help Entry**: Update help database
4. **Consider Permissions**: Check level requirements
5. **Test Edge Cases**: Invalid arguments, offline targets, etc.

#### When Modifying Game Mechanics
1. **Check Dependencies**: What other systems are affected?
2. **Maintain Balance**: Consider gameplay impact
3. **Update Documentation**: Modify relevant help files
4. **Test Thoroughly**: Verify all affected systems work
5. **Consider Backwards Compatibility**: Don't break existing content

#### When Working with Data Structures
1. **Understand Relationships**: How structures connect
2. **Maintain Integrity**: Ensure all references remain valid
3. **Handle Memory Properly**: Allocate and free correctly
4. **Consider Persistence**: How changes affect saved data
5. **Test Edge Cases**: Empty lists, maximum values, etc.

### Security Considerations

#### Input Validation
```c
// Always validate user input
void sanitize_input(char *input) {
  // Remove control characters
  // Limit length
  // Escape special characters
}

// Check permissions before actions
if (GET_LEVEL(ch) < LVL_IMMORT) {
  send_to_char(ch, "You don't have permission for that.\r\n");
  return;
}
```

#### SQL Injection Prevention
```c
// Use mysql_real_escape_string()
char escaped_name[MAX_NAME_LENGTH * 2 + 1];
mysql_real_escape_string(conn, escaped_name, player_name, strlen(player_name));

// Or use prepared statements for complex queries
```

### Performance Best Practices

#### Efficient Loops
```c
// Cache frequently accessed values
struct char_data *next_ch;
for (ch = character_list; ch; ch = next_ch) {
  next_ch = ch->next; // Cache before potential removal
  // ... process character ...
}
```

#### Memory Optimization
```c
// Use object pools for frequently allocated structures
// Minimize string operations in tight loops
// Cache expensive calculations
```

#### Database Optimization
```c
// Batch database operations when possible
// Use indexes on frequently queried columns
// Limit result sets with appropriate WHERE clauses
```

### Testing and Quality Assurance

#### Unit Testing
```c
// Use CuTest framework for unit tests
void test_combat_damage_calculation(CuTest *tc) {
  struct char_data *attacker = create_test_character();
  struct char_data *victim = create_test_character();

  int damage = calculate_damage(attacker, victim, NULL);

  CuAssertTrue(tc, damage >= 1); // Minimum damage
  CuAssertTrue(tc, damage <= 100); // Maximum reasonable damage

  cleanup_test_character(attacker);
  cleanup_test_character(victim);
}
```

#### Integration Testing
- Test with existing world data
- Verify compatibility with saved player files
- Check interaction with other systems
- Test under load conditions

### Documentation Standards

#### Function Documentation
```c
/**
 * Calculate initiative for combat ordering
 *
 * @param ch The character rolling initiative
 * @return Initiative value (1d20 + modifiers)
 */
int calculate_initiative(struct char_data *ch) {
  // Implementation...
}
```

#### Code Comments
```c
// Single line for simple explanations
int damage = roll_damage(weapon);

/* Multi-line for complex logic
 * This section handles the interaction between
 * multiple combat modifiers and special cases
 */
```

### Useful Macros and Functions

#### Character Macros
```c
GET_NAME(ch)          // Character name
GET_LEVEL(ch)         // Character level
GET_CLASS(ch)         // Character class
GET_RACE(ch)          // Character race
GET_STR(ch)           // Strength score
GET_HIT(ch)           // Current hit points
GET_MAX_HIT(ch)       // Maximum hit points
IS_NPC(ch)            // Is character an NPC?
IN_ROOM(ch)           // Character's current room
```

#### Object Macros
```c
GET_OBJ_TYPE(obj)     // Object type
GET_OBJ_WEIGHT(obj)   // Object weight
GET_OBJ_COST(obj)     // Object cost
GET_OBJ_VAL(obj, n)   // Object value n
```

#### Utility Functions
```c
one_argument(input, output)     // Parse one argument
two_arguments(input, arg1, arg2) // Parse two arguments
skip_spaces(&ptr)               // Skip whitespace
str_cmp(str1, str2)            // Case-insensitive compare
is_abbrev(abbr, full)          // Check abbreviation
```

### Recent Changes and Patterns (2025)

#### Performance Optimizations
- Zone reset algorithm improvements (O(n²) to O(n))
- Mobile activity caching and optimization
- NPC casting system efficiency improvements
- Database query optimization patterns

#### Documentation Standards
- Comprehensive technical documentation
- API reference documentation
- Troubleshooting and maintenance guides
- Developer onboarding documentation

#### Code Quality Improvements
- Memory leak fixes and prevention
- Input validation enhancements
- Error handling standardization
- Build system modernization

---

*This guide is updated regularly to reflect current development practices and codebase changes. Always refer to the latest version when working with the code.*